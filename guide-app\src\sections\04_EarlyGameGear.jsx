import React from 'react';

const EarlyGameGear = () => {
  return (
    <section id="early-game-gear" className="section">
      <div className="card mx-6 my-8">
        <div className="card-header">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-4">
            Early Game Gear & Power Siphon (Level 12) Setup
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
        </div>

        <div className="card-content space-y-8">
          {/* Weapon Upgrade Priority */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-yellow-500/20">
            <h3 className="text-xl font-semibold text-yellow-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
              Upgrading Your Weapon for Power Siphon
            </h3>
            <p className="text-slate-300 leading-relaxed mb-6">
              To make Power Siphon feel good, you must upgrade your weapon regularly. Look for wands with the following stats, in order of priority:
            </p>
            <div className="space-y-3">
              {[
                { priority: 1, stat: "Flat Lightning Damage", color: "text-blue-400" },
                { priority: 2, stat: "Any other Flat Elemental Damage", color: "text-green-400" },
                { priority: 3, stat: "Critical Strike Multiplier", color: "text-red-400" },
                { priority: 4, stat: "Spell Damage", color: "text-purple-400" },
                { priority: 5, stat: "Accuracy (becomes important mid-campaign)", color: "text-orange-400" }
              ].map((item, index) => (
                <div key={index} className="flex items-center space-x-4 p-3 bg-slate-800/50 rounded-lg">
                  <div className="w-8 h-8 bg-yellow-500/20 text-yellow-400 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                    {item.priority}
                  </div>
                  <span className={`${item.color} font-medium`}>{item.stat}</span>
                </div>
              ))}
            </div>
            <div className="mt-6 bg-amber-900/20 p-4 rounded-lg border border-amber-500/30">
              <p className="text-amber-200 text-sm">
                <span className="font-semibold">Pro Tip:</span> The unique wand <span className="text-amber-400 font-semibold">Doedre's Tenure</span> is an amazing and often cheap option that can carry you for a long time.
              </p>
            </div>
          </div>

          {/* Early Maps Gear Checklist */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-emerald-500/20">
            <h3 className="text-xl font-semibold text-emerald-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              White / Early Maps Gear Checklist
            </h3>
            <p className="text-slate-300 leading-relaxed mb-6">
              Once you enter maps, your campaign gear will quickly become inadequate. Focus on these upgrades:
            </p>
            <div className="grid gap-4">
              {[
                {
                  title: "Fix Your Flasks",
                  description: "They are a huge source of defense. Get immunity to a damaging ailment like Ignite. A Jade Flask is mandatory. Try to get another flask with \"Increased Evasion During Effect\".",
                  icon: "M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",
                  color: "blue"
                },
                {
                  title: "Upgrade Base Types",
                  description: "Look for high-tier Evasion/ES bases. The difference between campaign bases and T1/T2 map bases is enormous.",
                  icon: "M7 16a4 4 0 11-.02-7.927c.97-.757 2.218-1.17 3.52-1.073 2.291.17 4.398 1.628 5.24 3.83 1.411.296 2.462 1.538 2.462 3.17 0 1.657-1.343 3-3 3H7z",
                  color: "green"
                },
                {
                  title: "Craft a Better Wand",
                  description: "Use a Wrath essence on a good wand base, or buy a decent one from trade.",
                  icon: "M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",
                  color: "purple"
                },
                {
                  title: "Craft a Better Belt",
                  description: "Use a Zeal essence on a Crystal Belt. They are excellent until you can afford a Stygian Vise.",
                  icon: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
                  color: "orange"
                }
              ].map((item, index) => (
                <div key={index} className={`p-5 bg-slate-800/50 rounded-lg border border-${item.color}-500/20 hover:border-${item.color}-500/40 transition-colors duration-200`}>
                  <div className="flex items-start space-x-4">
                    <div className={`w-10 h-10 bg-${item.color}-500/20 text-${item.color}-400 rounded-lg flex items-center justify-center flex-shrink-0`}>
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={item.icon} />
                      </svg>
                    </div>
                    <div>
                      <h4 className={`font-semibold text-${item.color}-400 mb-2`}>{item.title}</h4>
                      <p className="text-slate-300 text-sm leading-relaxed">
                        {item.description.split(/(\b(?:Jade Flask|Increased Evasion During Effect|Wrath essence|Zeal essence|Crystal Belt|Stygian Vise)\b)/g).map((part, i) =>
                          ['Jade Flask', 'Increased Evasion During Effect', 'Wrath essence', 'Zeal essence', 'Crystal Belt', 'Stygian Vise'].includes(part) ?
                          <span key={i} className="text-amber-400 font-semibold">{part}</span> : part
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EarlyGameGear;