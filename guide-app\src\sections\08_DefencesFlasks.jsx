import React from 'react';

const DefencesFlasks = () => {
  return (
    <section id="defences-flasks" className="prose prose-invert max-w-none px-6 pb-12">
      <h2>Defences & Flasks</h2>

      <h3>Core Defensive Layers</h3>
      <ul>
        <li><strong><PERSON> (Level 24):</strong> This is your primary defensive aura. High evasion is key to survival.</li>
        <li><strong>Spell Suppression:</strong> Aim for 100% spell suppression chance. This is a massive damage reduction layer against spells. The Spellbreaker ascendancy is a huge help here.</li>
        <li><strong>Freeze:</strong> A reliable freeze is a powerful defensive tool. By converting all your physical damage to cold, you can freeze almost everything, preventing them from hitting you.</li>
        <li><strong>Endurance Charges:</strong> These provide physical damage reduction and elemental resistances. You can generate them easily via an <em>Enduring Composure</em> small cluster jewel or from an implicit on your chest armor.</li>
      </ul>

      <h3>Flask Setup</h3>
      <p>Your flask setup is critical for both defense and offense. Here is a recommended setup:</p>
      <ol>
        <li><strong>Life Flask:</strong> A Seething or Bubbling Divine Life Flask with bleed/corrupted blood immunity.</li>
        <li><strong>Jade Flask:</strong> Mandatory for high evasion. Roll for increased evasion rating during effect.</li>
        <li><strong>Diamond Flask:</strong> Your primary source of reliable critical strikes. Roll for increased crit chance during effect.</li>
        <li><strong>Quicksilver Flask:</strong> For speed. Roll for increased movement speed or effect.</li>
        <li><strong>Utility/Unique Flask:</strong> A Granite Flask for more armor, a Silver Flask for Onslaught, or a unique flask like <em>Atziri's Promise</em> or <em>Taste of Hate</em> for more damage and defense.</li>
      </ol>
      <p>
        Make sure to get immunity to Freeze, Ignite, and Shock on your utility flasks.
      </p>
    </section>
  );
};

export default DefencesFlasks;