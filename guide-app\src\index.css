@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --gaming-bg: #0a0e1a;
    --gaming-bg-secondary: #1e293b;
    --gaming-accent: #6366f1;
    --gaming-gold: #f59e0b;
    --gaming-text: #f8fafc;
    --gaming-text-muted: #94a3b8;
  }

  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-slate-900 text-slate-100 font-sans;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
    background-attachment: fixed;
    font-family: 'Inter', system-ui, sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Enhanced <PERSON><PERSON> Styles */
  .btn-primary {
    @apply px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white font-medium rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 active:scale-95;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-gradient-to-r from-amber-500 to-orange-500 text-slate-900 font-medium rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 active:scale-95;
  }

  .btn-ghost {
    @apply px-4 py-2 bg-slate-800/50 text-slate-200 border border-indigo-500/30 rounded-lg transition-all duration-300 hover:bg-indigo-500/20 hover:border-indigo-500/60 hover:shadow-lg;
  }

  /* Card Styles */
  .card {
    @apply bg-gradient-to-br from-slate-800/80 to-slate-900/90 border border-white/10 rounded-xl shadow-lg hover:shadow-xl hover:border-white/20;
  }

  .card-header {
    @apply p-6 border-b border-white/10;
  }

  .card-content {
    @apply p-6;
  }

  /* Navigation Styles */
  .nav-item {
    @apply block rounded-lg px-4 py-3 text-sm font-medium transition-all duration-300 relative overflow-hidden;
  }

  .nav-item::before {
    @apply absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-blue-500/20 opacity-0 transition-opacity duration-300;
    content: '';
  }

  .nav-item:hover::before {
    @apply opacity-100;
  }

  .nav-item.active {
    @apply bg-gradient-to-r from-indigo-600 to-blue-600 text-white shadow-lg;
  }

  .nav-item:not(.active) {
    @apply text-slate-400 hover:text-slate-200;
  }

  /* Section Styles */
  .section {
    @apply relative;
  }

  .section::before {
    @apply absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-indigo-500 to-blue-500 opacity-60;
    content: '';
  }

  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-slate-900;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-indigo-500/50 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-indigo-500/70;
  }

  /* Smooth animations for all elements */
  * {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  /* Focus styles for accessibility */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible {
    @apply outline-none ring-2 ring-indigo-500 ring-offset-2 ring-offset-slate-900;
  }

  /* Loading animation for dynamic content */
  .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Pulse animation for active elements */
  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  @keyframes pulseGlow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(99, 102, 241, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(99, 102, 241, 0.8);
    }
  }
}