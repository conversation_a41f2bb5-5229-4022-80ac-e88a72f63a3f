import React from 'react';

const TransitionReap = () => {
  return (
    <section id="transition-reap" className="section">
      <div className="card mx-6 my-8">
        <div className="card-header">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-4">
            Transitioning from Power Siphon (Level 12) to Exsanguinate/Reap (Level 28)
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
        </div>

        <div className="card-content space-y-8">
          {/* Disclaimer */}
          <div className="bg-red-900/20 p-6 rounded-xl border border-red-500/30">
            <div className="flex items-start space-x-3">
              <svg className="w-6 h-6 text-red-400 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <div>
                <h3 className="text-red-400 font-bold text-lg mb-2">DISCLAIMER</h3>
                <p className="text-red-200 leading-relaxed">
                  This transition requires careful planning. The nerfs to Power Siphon might make it attractive to switch earlier, but do not attempt this swap without meeting the requirements below. Always use Path of Building (PoB) to simulate the changes before spending currency.
                </p>
              </div>
            </div>
          </div>

          {/* Transition Checklist */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-cyan-500/20">
            <h3 className="text-xl font-semibold text-cyan-400 mb-6 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Transition Checklist (Levels 88-93)
            </h3>
            <p className="text-slate-300 mb-6">Before transitioning, you must have:</p>
            <div className="space-y-4">
              {[
                {
                  requirement: "5-Link armor minimum, 6-Link is highly recommended",
                  icon: "M7 16a4 4 0 11-.02-7.927c.97-.757 2.218-1.17 3.52-1.073 2.291.17 4.398 1.628 5.24 3.83 1.411.296 2.462 1.538 2.462 3.17 0 1.657-1.343 3-3 3H7z",
                  color: "blue"
                },
                {
                  requirement: "A minimum level 18 Exsanguinate (Level 12), ideally 20 or 21. Gem levels are a massive source of damage",
                  icon: "M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",
                  color: "green"
                },
                {
                  requirement: "The correct spell modifiers on your gear: +1 to physical gems on your weapon, +1 gems or high spell damage on your shield, and spell critical strike chance",
                  icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
                  color: "purple"
                },
                {
                  requirement: "100% Physical to Cold Conversion. This is not optional. Use Hrimsorrow gloves, a Watcher's Eye, Betrayal gloves, or the 40% Cold Mastery on the tree",
                  icon: "M7 16a4 4 0 11-.02-7.927c.97-.757 2.218-1.17 3.52-1.073 2.291.17 4.398 1.628 5.24 3.83 1.411.296 2.462 1.538 2.462 3.17 0 1.657-1.343 3-3 3H7z",
                  color: "indigo"
                },
                {
                  requirement: "A way to manage the life cost. A Kikazaru ring or 250-300+ life regeneration is necessary to not kill yourself",
                  icon: "M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z",
                  color: "red"
                },
                {
                  requirement: "At least 70% critical strike chance in PoB after the swap. Anything less will feel unreliable",
                  icon: "M13 10V3L4 14h7v7l9-11h-7z",
                  color: "yellow"
                }
              ].map((item, index) => (
                <div key={index} className={`flex items-start space-x-4 p-4 bg-slate-800/50 rounded-lg border border-${item.color}-500/20`}>
                  <div className={`w-8 h-8 bg-${item.color}-500/20 text-${item.color}-400 rounded-lg flex items-center justify-center flex-shrink-0 mt-1`}>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={item.icon} />
                    </svg>
                  </div>
                  <p className="text-slate-300 text-sm leading-relaxed">
                    {item.requirement.split(/(\b(?:5-Link|6-Link|Exsanguinate|Hrimsorrow|Watcher's Eye|Betrayal gloves|Cold Mastery|Kikazaru|PoB)\b)/g).map((part, i) =>
                      ['5-Link', '6-Link', 'Exsanguinate (Level 12)', 'Hrimsorrow', "Watcher's Eye", 'Betrayal gloves', 'Cold Mastery', 'Kikazaru', 'PoB'].includes(part) ?
                      <span key={i} className="text-amber-400 font-semibold">{part}</span> : part
                    )}
                  </p>
                </div>
              ))}
            </div>
            <div className="mt-6 bg-blue-900/20 p-4 rounded-lg border border-blue-500/30">
              <p className="text-blue-200 text-sm">
                <span className="font-semibold">Optional:</span> A <span className="text-amber-400 font-semibold">Heatshiver</span> helmet is a nice damage boost but not required for the transition itself.
              </p>
            </div>
          </div>

          {/* Skill Tree Adjustments */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-emerald-500/20">
            <h3 className="text-xl font-semibold text-emerald-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 001-1v-1a2 2 0 114 0z" />
              </svg>
              Skill Tree Adjustments
            </h3>
            <p className="text-slate-300 leading-relaxed">
              You will need to spec into Freeze Duration nodes like <span className="text-amber-400 font-semibold">Winter's Embrace</span> for your defense to be effective. To achieve a baseline 0.3s freeze on a mob, you must deal 5% of its health in a single hit. With 100% increased freeze duration, this is reduced to only 2.5%, making it much easier to freeze enemies and proc <span className="text-amber-400 font-semibold">Heatshiver</span>.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TransitionReap;