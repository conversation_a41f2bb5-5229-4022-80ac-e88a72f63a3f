import React from 'react';

const LevelingWalkthrough = () => {
  const levelingMilestones = [
    {
      level: "Level 1-4",
      location: "The Twilight Strand → Tidal Island",
      color: "emerald",
      icon: "M13 10V3L4 14h7v7l9-11h-7z",
      tasks: [
        "Start with any basic attack skill",
        "Look for 2-link items (any colors)"
      ],
      gems: [],
      notes: "Focus on reaching Tidal Island quickly"
    },
    {
      level: "Level 4",
      location: "After Tidal Island",
      color: "blue",
      icon: "M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",
      tasks: [
        "Buy Rolling Magma (Level 1) from vendor",
        "Buy Elemental Proliferation (Level 8) support",
        "Buy Flame Wall (Level 4)",
        "Look for 2-3 link items"
      ],
      gems: [
        {
          setup: "Main Clear",
          links: ["Rolling Magma (Level 1)", "Elemental Proliferation (Level 8)"],
          sockets: "2-Link",
          priority: "Essential"
        },
        {
          setup: "Utility",
          links: ["Flame Wall (Level 4)"],
          sockets: "1-Link",
          priority: "Important",
          notes: "Cast magma through flame wall for extra fire damage"
        }
      ],
      notes: "This is your core setup until level 31. Rolling Magma + Elemental Proliferation clears packs very well."
    },
    {
      level: "Level 8",
      location: "After Brutus",
      color: "purple",
      icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
      tasks: [
        "Add damage support to Rolling Magma (Level 1)",
        "Get Combustion from vendor after Brutus",
        "Look for 3-link items"
      ],
      gems: [
        {
          setup: "Main Clear",
          links: ["Rolling Magma (Level 1)", "Elemental Proliferation (Level 8)", "Added Cold/Lightning Damage"],
          sockets: "3-Link",
          priority: "Essential"
        },
        {
          setup: "Alternative Support",
          links: ["Combustion"],
          sockets: "Replace Added Damage",
          priority: "Good",
          notes: "Swap in Combustion after killing Brutus"
        }
      ],
      notes: "Add any available damage support. Combustion becomes available after Brutus fight."
    },
    {
      level: "Level 12",
      location: "The Cavern of Wrath",
      color: "red",
      icon: "M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z",
      tasks: [
        "Pick up Fire Trap (Level 12) for single target",
        "Complete Act 1",
        "Look for 3-4 link items"
      ],
      gems: [
        {
          setup: "Main Clear",
          links: ["Rolling Magma", "Elemental Proliferation", "Combustion"],
          sockets: "3-Link",
          priority: "Essential"
        },
        {
          setup: "Single Target",
          links: ["Fire Trap (Level 12)"],
          sockets: "1-Link",
          priority: "Important",
          notes: "Will link with supports in Act 2"
        }
      ],
      notes: "Fire Trap provides much needed single target damage for bosses."
    },
    {
      level: "Level 13-18",
      location: "Act 2 - Early",
      color: "orange",
      icon: "M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253",
      tasks: [
        "Complete Yeena's quest for gems",
        "Link Fire Trap (Level 12) with supports",
        "Start leveling Exsanguinate in off-hand",
        "Choose your bandit (recommend Kraityn for movement speed)"
      ],
      gems: [
        {
          setup: "Main Clear",
          links: ["Rolling Magma (Level 1)", "Elemental Proliferation (Level 8)", "Combustion"],
          sockets: "3-Link",
          priority: "Essential"
        },
        {
          setup: "Single Target",
          links: ["Fire Trap (Level 12)", "Trap and Mine Damage", "Swift Assembly"],
          sockets: "3-Link",
          priority: "Essential"
        },
        {
          setup: "Off-hand Leveling",
          links: ["Exsanguinate (multiple copies)"],
          sockets: "Any available",
          priority: "Important",
          notes: "Level as many copies as possible for corruption later"
        }
      ],
      notes: "This is your core setup for the rest of the campaign until level 31."
    },
    {
      level: "Level 31",
      location: "Act 3 - Library Quest Complete",
      color: "indigo",
      icon: "M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4",
      tasks: [
        "Complete Siosa quest in Library",
        "Acquire Charged Mines (Level 31) support",
        "Transition to Power Siphon (Level 12) + Locus Mines (Level 34)",
        "This is a major power spike!"
      ],
      gems: [
        {
          setup: "New Main Skill",
          links: ["Power Siphon (Level 12)", "Locus Mines (Level 34)", "Charged Mines (Level 31)"],
          sockets: "3-Link minimum",
          priority: "Essential"
        },
        {
          setup: "Add 4th Support",
          links: ["Added Lightning Damage (Level 8)", "Added Cold Damage (Level 8)", "Elemental Damage with Attacks"],
          sockets: "4-Link if available",
          priority: "Good",
          notes: "Add any available damage support"
        },
        {
          setup: "Continue Leveling",
          links: ["Exsanguinate (multiple copies)"],
          sockets: "Off-hand",
          priority: "Important"
        }
      ],
      notes: "Major transition point! Power Siphon (Level 12) + Locus Mines (Level 34) will carry you to maps."
    },
    {
      level: "Level 31+",
      location: "Act 4 and Beyond",
      color: "cyan",
      icon: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z",
      tasks: [
        "Get Grace aura in Act 4 - MANDATORY",
        "Regularly upgrade your wand",
        "Complete all Labyrinths",
        "Cap resistances (75%)",
        "Get 5-link armor for maps"
      ],
      gems: [
        {
          setup: "Power Siphon (4-5 Link)",
          links: ["Power Siphon (Level 12)", "Locus Mines (Level 34)", "Charged Mines (Level 31)", "Added Lightning", "Elemental Damage with Attacks"],
          sockets: "4-5 Link",
          priority: "Essential"
        },
        {
          setup: "Essential Aura",
          links: ["Grace (Level 24)"],
          sockets: "Any",
          priority: "MANDATORY",
          notes: "You will feel too squishy without Grace"
        },
        {
          setup: "Movement",
          links: ["Flame Dash (Level 10)", "Arcane Surge (Level 1)"],
          sockets: "2-Link",
          priority: "Important"
        },
        {
          setup: "Continue Leveling",
          links: ["Exsanguinate", "Reap (Level 28 if quality)"],
          sockets: "Off-hand",
          priority: "Important"
        }
      ],
      notes: "Grace aura is absolutely essential. Focus on weapon upgrades for Power Siphon damage."
    }
  ];

  return (
    <section id="leveling-walkthrough" className="section">
      <div className="card mx-6 my-8">
        <div className="card-header">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-4">
            Complete Leveling Walkthrough with Gem Links
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
        </div>

        <div className="card-content space-y-8">
          {/* Vendor Search Strings */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-purple-500/20">
            <h3 className="text-xl font-semibold text-purple-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Vendor Search String (Copy This!)
            </h3>
            <p className="text-slate-300 leading-relaxed mb-4">Use this regex in the vendor window to quickly find useful items:</p>
            <div className="bg-slate-800 p-4 rounded-lg border border-slate-600">
              <code className="text-green-400 font-mono text-sm">b-b-b|g-g-b|g-[gb]-g|b-g-g|nne|rint|ll g|Earn</code>
            </div>
            <p className="text-slate-400 text-sm mt-3">This highlights linked sockets, movement speed boots, and +1 gem level wands.</p>
          </div>

          {/* Leveling Milestones with Integrated Gem Links */}
          <div className="space-y-6">
            {levelingMilestones.map((milestone, index) => (
              <div key={index} className={`bg-slate-900/30 p-6 rounded-xl border border-${milestone.color}-500/20`}>
                <div className="flex items-start space-x-4 mb-6">
                  <div className={`w-12 h-12 bg-${milestone.color}-500/20 text-${milestone.color}-400 rounded-lg flex items-center justify-center flex-shrink-0`}>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={milestone.icon} />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className={`text-xl font-bold text-${milestone.color}-400 mb-1`}>{milestone.level}</h3>
                    <p className="text-slate-400 text-sm mb-4">{milestone.location}</p>

                    {/* Tasks */}
                    <div className="mb-6">
                      <h4 className="text-slate-200 font-semibold mb-3">Tasks to Complete:</h4>
                      <ul className="space-y-2">
                        {milestone.tasks.map((task, taskIndex) => (
                          <li key={taskIndex} className="flex items-start space-x-2">
                            <div className={`w-2 h-2 bg-${milestone.color}-400 rounded-full mt-2 flex-shrink-0`}></div>
                            <span className="text-slate-300 text-sm">{task}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Gem Links */}
                    {milestone.gems.length > 0 && (
                      <div className="space-y-4">
                        <h4 className="text-slate-200 font-semibold mb-3">Gem Links Setup:</h4>
                        {milestone.gems.map((gemSetup, gemIndex) => (
                          <div key={gemIndex} className="bg-slate-800/50 p-4 rounded-lg">
                            <div className="flex items-center justify-between mb-3">
                              <span className={`text-${milestone.color}-400 font-medium`}>{gemSetup.setup}</span>
                              <div className="flex items-center space-x-2">
                                <span className="text-xs text-slate-400">{gemSetup.sockets}</span>
                                <span className={`px-2 py-1 text-xs rounded-full ${
                                  gemSetup.priority === 'Essential' ? 'bg-red-500/20 text-red-400' :
                                  gemSetup.priority === 'Important' ? 'bg-amber-500/20 text-amber-400' :
                                  'bg-blue-500/20 text-blue-400'
                                }`}>
                                  {gemSetup.priority}
                                </span>
                              </div>
                            </div>
                            <div className="flex flex-wrap gap-2 mb-2">
                              {gemSetup.links.map((gem, gemLinkIndex) => (
                                <span key={gemLinkIndex} className="px-3 py-1 bg-amber-500/20 text-amber-300 text-sm rounded-full font-medium">
                                  {gem}
                                </span>
                              ))}
                            </div>
                            {gemSetup.notes && (
                              <p className="text-slate-400 text-sm mt-2">{gemSetup.notes}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Notes */}
                    {milestone.notes && (
                      <div className={`mt-6 bg-${milestone.color}-900/20 p-4 rounded-lg border border-${milestone.color}-500/30`}>
                        <p className={`text-${milestone.color}-200 text-sm`}>
                          <span className="font-semibold">💡 Pro Tip:</span> {milestone.notes}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Transition to Exsanguinate/Reap */}
          <div className="bg-gradient-to-r from-red-900/20 to-orange-900/20 p-6 rounded-xl border border-red-500/30 mt-8">
            <h3 className="text-xl font-semibold text-red-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
              Transition to Exsanguinate/Reap (Levels 88-93)
            </h3>
            <p className="text-red-200 mb-6 leading-relaxed">
              <span className="font-bold">⚠️ IMPORTANT:</span> Do not attempt this transition without meeting ALL requirements below. Always simulate in Path of Building first!
            </p>

            <div className="grid gap-4 mb-6">
              <h4 className="text-red-300 font-semibold">Required Before Transition:</h4>
              {[
                "5-Link armor minimum (6-Link highly recommended)",
                "Level 18+ Exsanguinate (ideally 20-21)",
                "100% Physical to Cold Conversion (Hrimsorrow gloves, Watcher's Eye, or 40% Cold Mastery)",
                "Life cost management (Kikazaru ring or 250-300+ life regen)",
                "70%+ critical strike chance in PoB",
                "Correct spell modifiers on gear (+1 gems, spell crit chance)"
              ].map((req, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-red-900/20 rounded-lg">
                  <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-red-200 text-sm">{req}</span>
                </div>
              ))}
            </div>

            <div className="bg-slate-800/50 p-5 rounded-lg">
              <h4 className="text-amber-400 font-semibold mb-3">Endgame 6-Link Setups:</h4>
              <div className="space-y-4">
                <div className="bg-slate-900/50 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-red-400 font-medium">Exsanguinate (Primary)</span>
                    <span className="text-xs text-slate-400">6-Link</span>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {["Exsanguinate", "Unleash (Level 38)", "Cold Penetration", "Increased Critical Damage", "Increased Critical Strikes", "Empower"].map((gem, index) => (
                      <span key={index} className="px-3 py-1 bg-amber-500/20 text-amber-300 text-sm rounded-full font-medium">
                        {gem}
                      </span>
                    ))}
                  </div>
                  <p className="text-slate-400 text-sm">For clear: swap Increased Critical Damage → Awakened Chain</p>
                </div>

                <div className="bg-slate-900/50 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-orange-400 font-medium">Reap (Bossing Alternative)</span>
                    <span className="text-xs text-slate-400">6-Link</span>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {["Reap", "Spell Cascade", "Cold Penetration", "Increased Critical Damage", "Concentrated Effect", "Empower"].map((gem, index) => (
                      <span key={index} className="px-3 py-1 bg-amber-500/20 text-amber-300 text-sm rounded-full font-medium">
                        {gem}
                      </span>
                    ))}
                  </div>
                  <p className="text-slate-400 text-sm">Requires gem swapping and blood charge generation</p>
                </div>
              </div>
            </div>
          </div>

          {/* Important Gem Notes */}
          <div className="bg-gradient-to-r from-amber-900/20 to-orange-900/20 p-6 rounded-xl border border-amber-500/30">
            <h3 className="text-amber-400 font-semibold mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Essential Gem Information
            </h3>
            <div className="grid gap-4">
              {[
                { title: "Exsanguinate Quality", info: "NOT important - we scale the hit, not DoT" },
                { title: "Reap Quality", info: "HIGHLY desirable - damage per Blood Charge" },
                { title: "Level 21 Gems", info: "Massive damage increase - corrupt multiple copies. Level 1 for Empower" },
                { title: "Empower Level 4", info: "Huge damage boost but expensive" },
                { title: "Support Priority", info: "Cold Penetration > Crit Multi > Crit Chance" }
              ].map((note, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-amber-900/20 rounded-lg">
                  <div className="w-2 h-2 bg-amber-400 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <span className="text-amber-300 font-semibold text-sm">{note.title}:</span>
                    <span className="text-amber-200 text-sm ml-2">{note.info}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LevelingWalkthrough;