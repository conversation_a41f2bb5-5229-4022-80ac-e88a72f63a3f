import React from 'react';
import Sidebar from './components/Sidebar';
import ChecklistDrawer from './components/ChecklistDrawer';
import DarkModeToggle from './components/DarkModeToggle';
import ScrollToTop from './components/ScrollToTop';

import BuildConcept from './sections/01_BuildConcept';
import CoreSetup from './sections/02_CoreSetup';
import LevelingWalkthrough from './sections/03_LevelingWalkthrough';
import EarlyGameGear from './sections/04_EarlyGameGear';
import TransitionReap from './sections/05_TransitionReap';
import MidEndGameGear from './sections/06_MidEndGameGear';
import AtlasStrategy from './sections/07_AtlasStrategy';
import DefencesFlasks from './sections/08_DefencesFlasks';
import FinalGemLinks from './sections/09_FinalGemLinks';
import ChecklistOverview from './sections/10_ChecklistOverview';

function App() {
  const handleCopyPob = () => {
    navigator.clipboard.writeText('STUB_POB_STRING');
    // In a real app, you'd show a toast notification here
    console.log('PoB copied!');
  };

  return (
    <div className="flex h-screen bg-slate-900 text-slate-100 overflow-hidden">
      <Sidebar />
      <main className="flex-1 flex flex-col overflow-hidden">
        <header className="flex justify-between items-center p-6 bg-slate-800/80 backdrop-blur-md border-b border-white/10 sticky top-0 z-20 shadow-lg">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-gradient-to-br from-indigo-600 to-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">PoE</span>
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent">
              Path of Exile 3.26 Reap Guide
            </h1>
          </div>
          <div className="flex items-center gap-4">
            <button
              onClick={handleCopyPob}
              className="btn-primary group relative overflow-hidden"
            >
              <span className="relative z-10 flex items-center space-x-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <span>Copy PoB</span>
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
            <DarkModeToggle />
            <ChecklistDrawer />
          </div>
        </header>
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-6xl mx-auto">
            <BuildConcept />
            <CoreSetup />
            <LevelingWalkthrough />
            <EarlyGameGear />
            <TransitionReap />
            <MidEndGameGear />
            <AtlasStrategy />
            <DefencesFlasks />
            <FinalGemLinks />
            <ChecklistOverview />
          </div>
        </div>
      </main>
      <ScrollToTop />
    </div>
  );
}

export default App;