import React from 'react';

const ChecklistOverview = () => {
  const levelRanges = [
    {
      title: "Levels 1-12",
      color: "emerald",
      icon: "M13 10V3L4 14h7v7l9-11h-7z",
      tasks: [
        "Get Rolling Magma (Level 1) + Elemental Proliferation (Level 8).",
        "Get Flame Wall (Level 4) for extra damage.",
        "Get Fire Trap (Level 12) for single target.",
        "Look for 3-link items (BBB, GGB)."
      ]
    },
    {
      title: "Levels 13-31",
      color: "blue",
      icon: "M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253",
      tasks: [
        "Complete the Library quest for gems.",
        "Link Fire Trap with Trap and Mine Damage.",
        "Decide on your Bandit choice.",
        "Start leveling Exsanguinate gems in off-hand.",
        "Transition to Power Siphon + Locus Mines at level 31."
      ]
    },
    {
      title: "Levels 32-55",
      color: "purple",
      icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
      tasks: [
        "Get Grace aura.",
        "Complete first Labyrinth.",
        "Regularly upgrade your wand for Power Siphon damage.",
        "Look for 4-link items."
      ]
    },
    {
      title: "Levels 56-70 (End of Campaign)",
      color: "orange",
      icon: "M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z",
      tasks: [
        "Complete second and third Labyrinths.",
        "Cap your elemental resistances (75%).",
        "Get a 5-link armor piece.",
        "Prepare for the transition to Exsanguinate."
      ]
    },
    {
      title: "Early Maps",
      color: "red",
      icon: "M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7",
      tasks: [
        "Fix your flasks! Get ailment immunities.",
        "Upgrade gear to T1/T2 bases.",
        "Complete fourth Labyrinth.",
        "Start working on your Atlas completion.",
        "Acquire all necessary items for the Exsanguinate transition."
      ]
    }
  ];

  return (
    <section id="checklist-overview" className="section">
      <div className="card mx-6 my-8">
        <div className="card-header">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-4">
            Leveling Checklist Overview
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
        </div>

        <div className="card-content">
          <p className="text-slate-300 leading-relaxed mb-8 text-lg">
            This is a quick checklist to keep you on track during the campaign and into early maps. Use the checklist in the drawer for a more interactive experience.
          </p>

          <div className="space-y-6">
            {levelRanges.map((range, index) => (
              <div key={index} className={`bg-slate-900/30 p-6 rounded-xl border border-${range.color}-500/20`}>
                <h3 className={`text-xl font-semibold text-${range.color}-400 mb-4 flex items-center`}>
                  <div className={`w-8 h-8 bg-${range.color}-500/20 text-${range.color}-400 rounded-lg flex items-center justify-center mr-3`}>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={range.icon} />
                    </svg>
                  </div>
                  {range.title}
                </h3>
                <div className="grid gap-3">
                  {range.tasks.map((task, taskIndex) => (
                    <div key={taskIndex} className="flex items-start space-x-3 p-3 bg-slate-800/50 rounded-lg">
                      <div className={`w-2 h-2 bg-${range.color}-400 rounded-full mt-2 flex-shrink-0`}></div>
                      <span className="text-slate-300 text-sm leading-relaxed">
                        {task.split(/(\b(?:Rolling Magma|Elemental Proliferation|Flame Wall|Fire Trap|Library|Trap and Mine Damage|Bandit|Exsanguinate|Power Siphon|Locus Mines|Grace (Level 24)|Labyrinth|resistances)\b)/g).map((part, i) =>
                          ['Rolling Magma', 'Elemental Proliferation', 'Flame Wall', 'Fire Trap', 'Library', 'Trap and Mine Damage', 'Bandit', 'Exsanguinate', 'Power Siphon', 'Locus Mines', 'Grace', 'Labyrinth', 'resistances'].includes(part) ?
                          <span key={i} className="text-amber-400 font-semibold">{part}</span> : part
                        )}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 bg-gradient-to-r from-indigo-900/20 to-blue-900/20 p-6 rounded-xl border border-indigo-500/30">
            <div className="flex items-center space-x-3 mb-3">
              <svg className="w-6 h-6 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h4 className="text-indigo-400 font-semibold">Pro Tip</h4>
            </div>
            <p className="text-slate-300 text-sm leading-relaxed">
              Use the interactive checklist in the drawer (top-right button) to track your progress and get detailed task descriptions. Each completed task will help you stay on track for a smooth leveling experience.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ChecklistOverview;