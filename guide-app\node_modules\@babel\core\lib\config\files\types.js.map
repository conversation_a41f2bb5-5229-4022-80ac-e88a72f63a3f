{"version": 3, "names": [], "sources": ["../../../src/config/files/types.ts"], "sourcesContent": ["import type { InputOptions } from \"../index.ts\";\n\nexport type ConfigFile = {\n  filepath: string;\n  dirname: string;\n  options: InputOptions & { babel?: unknown };\n};\n\nexport type IgnoreFile = {\n  filepath: string;\n  dirname: string;\n  ignore: Array<RegExp>;\n};\n\nexport type RelativeConfig = {\n  // The actual config, either from package.json#babel, .babelrc, or\n  // .babelrc.js, if there was one.\n  config: ConfigFile | null;\n  // The .babelignore, if there was one.\n  ignore: IgnoreFile | null;\n};\n\nexport type FilePackageData = {\n  // The file in the package.\n  filepath: string;\n  // Any ancestor directories of the file that are within the package.\n  directories: Array<string>;\n  // The contents of the package.json. May not be found if the package just\n  // terminated at a node_modules folder without finding one.\n  pkg: ConfigFile | null;\n  // True if a package.json or node_modules folder was found while traversing\n  // the directory structure.\n  isPackage: boolean;\n};\n"], "mappings": "", "ignoreList": []}